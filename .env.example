# Authentication Configuration
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your_secure_password_here
JWT_SECRET=your_jwt_secret_key_at_least_32_characters_long

# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration
DATABASE_URL=postgresql://postgres:your_password@localhost:5432/marketing_automation

# Database Pool Configuration (optional)
DB_POOL_MIN=2
DB_POOL_MAX=10
DB_ACQUIRE_TIMEOUT=30000

# Production Database Example (Railway)
# DATABASE_URL=postgresql://postgres:<EMAIL>:5432/railway?sslmode=require

# Email Service Configuration
POSTMARK_TOKEN=your_postmark_server_token_here
POSTMARK_FROM_EMAIL=<EMAIL>
EMAIL_BATCH_SIZE=50
EMAIL_TIMEOUT_SECONDS=30
EMAIL_MAX_RECIPIENTS=500

# Template Configuration
TEMPLATE_CACHE_TTL=3600

# Session Configuration
SESSION_SECRET=your_session_secret_for_flash_messages

# Data Import Configuration
IMPORT_MAX_FILE_SIZE=2097152
IMPORT_BATCH_SIZE=100
IMPORT_TIMEOUT_MINUTES=10

# Job Scheduler Configuration
JOB_TIMEOUT_SECONDS=60
RETRY_COUNT=3
RETRY_INTERVAL_MINUTES=30

# ✅ ADD: Pipeline System Configuration
# Template Generation Settings
MAX_TEMPLATE_RETRIES=3
TEMPLATE_RETRY_DELAY_MINUTES=10
TEMPLATE_BATCH_SIZE=20
TEMPLATE_TIMEOUT_MINUTES=5

# Queue Processing Settings
QUEUE_SCAN_INTERVAL_SECONDS=60
QUEUE_BATCH_SIZE=50
MAX_PROCESSING_TIME_SECONDS=300

# Email Sending Settings
EMAIL_MAX_RECIPIENTS_PER_JOB=50
EMAIL_RETRY_DELAY_MINUTES=5
MAX_EMAIL_RETRIES=3

# Pipeline Performance Settings
PIPELINE_EXECUTION_TIMEOUT_SECONDS=300
MAX_CONCURRENT_PIPELINES=3
PIPELINE_LOG_RETENTION_DAYS=90

# Pipeline Development Settings
PIPELINE_DEBUG=false
MOCK_AI_GENERATION=false
SKIP_REAL_EMAIL_SENDING=false

# AI Service Configuration
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_TIMEOUT_SECONDS=30

# External Services (future use)
# Add other external service keys here