<div class="queue-container">
    <div class="page-header queue">
        <h1>Queue Management</h1>
        <p>Manage template generation queue and review pending templates</p>
    </div>

    <div class="page-content">
        {{!-- Flash Messages --}}
        {{#if success}}
            <div class="alert alert-success">{{success}}</div>
        {{/if}}
        {{#if error}}
            <div class="alert alert-error">{{error}}</div>
        {{/if}}
        {{#if warning}}
            <div class="alert alert-warning">{{warning}}</div>
        {{/if}}

        {{!-- Queue Statistics --}}
        <div class="card">
            <div class="card-header">
                <h3>Queue Statistics</h3>
            </div>
            <div class="card-content">
                <div class="queue-stats-grid">
                    <div class="queue-stat-card">
                        <div class="stat-number">{{queueStats.pending.waitingGeneration}}</div>
                        <div class="stat-label">Waiting Generation</div>
                    </div>
                    <div class="queue-stat-card">
                        <div class="stat-number">{{queueStats.pending.pendingReview}}</div>
                        <div class="stat-label">Pending Review</div>
                    </div>
                    <div class="queue-stat-card">
                        <div class="stat-number">{{queueStats.completed.approved}}</div>
                        <div class="stat-label">Approved</div>
                    </div>
                    <div class="queue-stat-card">
                        <div class="stat-number">{{queueStats.completed.rejected}}</div>
                        <div class="stat-label">Rejected</div>
                    </div>
                </div>
            </div>
        </div>

        {{!-- Templates for Review --}}
        {{#if hasTemplatesForReview}}
            <div class="card">
                <div class="card-header">
                    <h3>Templates Pending Review</h3>
                </div>
                <div class="card-content">
                    <div class="templates-review-list">
                        {{#each templatesForReview}}
                            <div class="template-review-item">
                                <div class="template-info">
                                    <h4>Template #{{templateId}}</h4>
                                    <p><strong>Customer:</strong> {{customerId}}</p>
                                    <p><strong>Pipeline:</strong> {{pipelineName}}</p>
                                    <p><strong>Generated:</strong> {{formatDate generatedAt}}</p>
                                    {{#if subject}}
                                        <p><strong>Subject:</strong> {{subject}}</p>
                                    {{/if}}
                                </div>
                                <div class="template-actions">
                                    <form method="POST" action="/admin/pipelines/templates/{{templateId}}/approve" style="display: inline;">
                                        <button type="submit" class="btn btn-success">Approve</button>
                                    </form>
                                    <form method="POST" action="/admin/pipelines/templates/{{templateId}}/reject" style="display: inline;">
                                        <button type="submit" class="btn btn-danger">Reject</button>
                                    </form>
                                    <a href="/api/templates/{{templateId}}/preview" class="btn btn-secondary" target="_blank">Preview</a>
                                </div>
                            </div>
                        {{/each}}
                    </div>
                </div>
            </div>
        {{else}}
            <div class="card">
                <div class="card-header">
                    <h3>Templates Pending Review</h3>
                </div>
                <div class="card-content">
                    <div class="empty-state">
                        <p>No templates are currently pending review.</p>
                        <a href="/admin/pipelines" class="btn btn-secondary">Back to Pipeline Management</a>
                    </div>
                </div>
            </div>
        {{/if}}

        {{!-- Queue Actions --}}
        <div class="card">
            <div class="card-header">
                <h3>Queue Actions</h3>
            </div>
            <div class="card-content">
                <div class="action-buttons">
                    <a href="/admin/pipelines" class="btn btn-secondary">Back to Pipelines</a>
                    {{#unless queueStats.processing}}
                        <form method="POST" action="/admin/pipelines/generate-templates" style="display: inline;">
                            <button type="submit" class="btn btn-primary">Generate Templates</button>
                        </form>
                    {{else}}
                        <button class="btn btn-primary" disabled>Generation in Progress...</button>
                    {{/unless}}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .queue-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem;
    }

    .page-header.queue {
        text-align: center;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #667eea;
    }

    .page-header.queue h1 {
        color: #667eea;
        margin-bottom: 0.5rem;
    }

    .queue-stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .queue-stat-card {
        text-align: center;
        padding: 1.5rem;
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e9ecef;
    }

    .queue-stat-card .stat-number {
        display: block;
        font-size: 2rem;
        font-weight: bold;
        color: #667eea;
        margin-bottom: 0.5rem;
    }

    .queue-stat-card .stat-label {
        color: #6c757d;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .templates-review-list {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .template-review-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        background: #f8f9fa;
    }

    .template-info h4 {
        margin: 0 0 0.5rem 0;
        color: #343a40;
    }

    .template-info p {
        margin: 0.25rem 0;
        color: #6c757d;
        font-size: 0.9rem;
    }

    .template-actions {
        display: flex;
        gap: 0.5rem;
        align-items: center;
    }

    .empty-state {
        text-align: center;
        padding: 2rem;
        color: #6c757d;
    }

    .empty-state p {
        margin-bottom: 1rem;
        font-size: 1.1rem;
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
    }

    @media (max-width: 768px) {
        .queue-container {
            padding: 1rem;
        }

        .template-review-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }

        .template-actions {
            width: 100%;
            justify-content: center;
        }

        .action-buttons {
            flex-direction: column;
            align-items: center;
        }
    }
</style>
