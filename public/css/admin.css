/* Reset and Base */
* { margin: 0; padding: 0; box-sizing: border-box; }

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f8f9fa;
    line-height: 1.6;
    color: #333;
}

/* Navigation */
.topbar {
    background-color: #343a40;
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 1rem;
}

.nav-brand a {
    color: white;
    text-decoration: none;
    font-size: 1.25rem;
    font-weight: 600;
}

.nav-links {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.nav-link {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.nav-link:hover { background-color: rgba(255, 255, 255, 0.1); }
.nav-link.active { background-color: #007bff; }
.nav-link.logout { background-color: #dc3545; margin-left: 1rem; }
.nav-link.logout:hover { background-color: #c82333; }

/* Content */
.content {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 1rem;
}

/* Forms */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #343a40;
}

.form-group input, .form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-group input:focus, .form-group select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* Buttons */
.btn {
    padding: 0.75rem 1rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover { background-color: #5a6268; }

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-success:hover { background-color: #218838; }

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover { background-color: #c82333; }

.btn:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
    transform: none;
}

.btn.full-width { width: 100%; }

/* Alerts */
.alert {
    padding: 0.75rem;
    border-radius: 6px;
    margin-bottom: 1rem;
    text-align: center;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
}

.alert-error {
    color: #721c24;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
}

.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
}

/* Login Page */
.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-form {
    background: white;
    padding: 2.5rem;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    width: 100%;
    max-width: 400px;
}

.login-header {
    text-align: center;
    margin-bottom: 2rem;
}

.login-header h1 {
    color: #343a40;
    margin-bottom: 0.5rem;
    font-size: 1.75rem;
}

.login-header p {
    color: #6c757d;
    font-size: 1rem;
}

/* Page Headers */
.page-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
    overflow: hidden;
}

.page-header {
    color: white;
    padding: 2rem;
    text-align: center;
}

.page-header.dashboard {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.page-header.import {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.page-header h1 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.page-header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.page-content {
    padding: 2rem;
}

/* Cards */
.card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.card-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e9ecef;
    background-color: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    margin: 0;
    color: #343a40;
    font-size: 1.1rem;
}

.card-content {
    padding: 1.5rem;
}

/* Grid Layouts */
.grid {
    display: grid;
    gap: 1.5rem;
}

.grid-2 { grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); }
.grid-3 { grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); }
.grid-4 { grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); }

/* Import Specific */
.file-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    background-color: #f8f9fa;
    transition: all 0.2s ease;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: #007bff;
    background-color: #f0f7ff;
}

.file-upload-area.selected {
    background-color: #e8f5e8;
    border-color: #28a745;
    color: #155724;
}

.file-upload-icon {
    font-size: 3rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

.file-input { display: none; }

.mode-selection {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.mode-option {
    border: 2px solid #dee2e6;
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.mode-option:hover {
    border-color: #007bff;
    background-color: #f8f9fa;
}

.mode-option.selected {
    border-color: #007bff;
    background-color: #e3f2fd;
}

.import-status {
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    text-align: center;
}

.import-status.importing {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

.import-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #ffc107;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s ease-in-out infinite;
    margin-right: 0.5rem;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin: 1rem 0;
}

.summary-item {
    text-align: center;
    padding: 1rem;
    background-color: white;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.summary-number {
    font-size: 2rem;
    font-weight: 700;
    color: #007bff;
    display: block;
}

.summary-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: 0.5rem;
}

.summary-item.success .summary-number { color: #28a745; }
.summary-item.error .summary-number { color: #dc3545; }

.error-report {
    background-color: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 1rem;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.85rem;
    max-height: 300px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* Status & Badges */
.status-badge, .badge {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-online, .badge-success {
    background-color: #d4edda;
    color: #155724;
}

.badge-warning {
    background-color: #fff3cd;
    color: #856404;
}

.badge-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* Responsive */
@media (max-width: 768px) {
    .nav-container { padding: 0 0.5rem; }
    .nav-links { gap: 0.25rem; }
    .nav-link { padding: 0.4rem 0.8rem; font-size: 0.9rem; }

    .content { margin: 1rem auto; padding: 0 0.5rem; }
    .login-form { padding: 2rem; margin: 1rem; }

    .page-header { padding: 1.5rem; }
    .page-header h1 { font-size: 1.5rem; }
    .page-content { padding: 1rem; }

    .grid-2, .grid-3, .grid-4 { grid-template-columns: 1fr; }
    .mode-selection { grid-template-columns: 1fr; }
    .summary-grid { grid-template-columns: 1fr 1fr; }
}

@media (max-width: 480px) {
    .login-container { padding: 1rem; }
    .login-form { padding: 1.5rem; }
    .card-content, .page-content { padding: 1rem; }
    .summary-grid { grid-template-columns: 1fr; }
    .file-upload-area { padding: 1.5rem; }
}