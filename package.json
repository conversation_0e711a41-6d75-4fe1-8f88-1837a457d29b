{"name": "marketing-automation-system", "version": "1.0.0", "description": "Node.js-based marketing automation system with code-based email campaigns", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "db:migrate": "node scripts/migrate.js", "db:status": "node scripts/db-status.js", "db:reset": "node scripts/db-reset.js", "db:seed": "node scripts/seed-data.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["marketing", "automation", "email", "campaigns", "nodejs", "postgresql", "csv-import", "data-management", "postmark", "email-templates"], "author": "Marketing Automation Team", "license": "MIT", "dependencies": {"express": "^4.18.0", "express-handlebars": "^7.0.0", "express-session": "^1.17.3", "jsonwebtoken": "^9.0.0", "cookie-parser": "^1.4.6", "dotenv": "^16.0.0", "pg": "^8.8.0", "csv-parser": "^3.0.0", "multer": "2.0.2", "mime-types": "^2.1.35", "postmark": "^4.0.0"}, "devDependencies": {"nodemon": "^3.0.0"}, "engines": {"node": ">=18.0.0"}}